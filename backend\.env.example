# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/nobify"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-this-in-production"

# Server Configuration
PORT=3001

# API Keys (optional - for external services)
COINGECKO_API_URL="https://api.coingecko.com/api/v3"
COINMARKETCAP_API_URL="https://pro-api.coinmarketcap.com/v1"
COINMARKETCAP_API_KEY=""

# SendGrid (for email notifications)
SENDGRID_API_KEY=""
SENDGRID_FROM_EMAIL="<EMAIL>"

# Firebase (for push notifications)
FIREBASE_PROJECT_ID=""
FIREBASE_PRIVATE_KEY=""
FIREBASE_CLIENT_EMAIL=""

# Frontend URL for email links
FRONTEND_URL="http://localhost:5173"

# Optional: Disable scheduling for testing
ALERT_SCHEDULING_ENABLED=true
